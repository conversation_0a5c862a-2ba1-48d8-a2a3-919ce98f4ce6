### Prompt 19: Other todos

### On login page:

Add logo
Change font style for "Dr. Muscle X - Login to continue your workout" to a modern pair that matches the "X" angle and slogan.
Replace "Login to continue your workout" with "World's Fastest AI Personal Trainer"

### On exercise page:

Reps remove 100 or less

Create .husky\pre-push file with:
#!/usr/bin/env sh
. "$(dirname -- "$0")/\_/husky.sh"

npx playwright test

Fix Claude Code Github actions (https://github.com/dr-muscle/DrMuscleWebApp/actions/runs/***********/job/***********)

Update home (program) page to add an horizontal module at the top, similar to top card in Dr. Muscle. This can be displayed quickly using Endpoint: POST /api/Account/GetUserInfoPyramid, make the loading experience smoother for home page.

Another way to make the loading experience smoother would be to show the tips overlays, similar to what we have on tap Start workout in the mobile app.

Bring back recovery card at the top of program page. It's commented out.

TDD
Chat with AI to see how best to implement
Create testing suite to run every time? Where?
Add to claude.md

Terra: install <PERSON>wright
install Playwright browsers
Readd terragon-setup.sh

<PERSON>'s comments
UI

- Yellow gradient
- Blue gradient

staging.dr-muscle.com

Commit 26109a4 was working

Claude Playwright MCP

Once workouts are loading, harden with tests etc so that a random change does not break them.
Write the steps for the test myself, asking AI to codify it. Then ask AI how to run it every time. Prevent commits when the test is failing. Then another for login, and another for exercise recos.

Terragon: Better PRs

Design: generate other color palettes and layout principles for mobile-first responsive design

Big cleanup: Remove "backward compatibility" with older implementations (this is not in production).

✅ Removed complex transitions - No more gold transitions, progressive loading, cross-fade animations

Commit 13e2aa0 IS WORKING -- NO RACE CONDITIONS!!!

Have AI describe in detail how workouts work, for Opus to implement in Web app. Create implementation plans.

Offline mode

Swaps are saved locally (not in database). Refactor swap feature to use same endpoints as edit workout. Swapping an exercise would actually remove it from the workout and add the new one instead, in the same position in the list (same order). https://www.terragonlabs.com/task/53723292-1741-4dd9-903b-b76719529b76

Add this to exercise page for better UX
Let's warm up:
Work sets:
Remove explainer bos entirely?

Latest working:
Still working in terragon/add-set-type-labels
fix/sets-display-numbering is close

Would need changes to support assisted pullups/dips
handle negative weights for assisted exercises
